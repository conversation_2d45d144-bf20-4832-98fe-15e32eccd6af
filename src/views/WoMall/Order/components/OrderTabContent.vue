<template>
  <div class="order-tab-content" ref="contentRef">
    <!-- 骨架屏 -->
    <div v-if="showSkeleton" class="skeleton-container">
      <div v-for="i in 3" :key="`skeleton-${i}`" class="order-item">
        <WoCard>
          <div class="skeleton-order">
            <!-- 订单头部骨架 -->
            <div class="skeleton-header">
              <div class="skeleton-order-number"></div>
              <div class="skeleton-status"></div>
            </div>

            <!-- 商品信息骨架 -->
            <div class="skeleton-goods">
              <div class="skeleton-image"></div>
              <div class="skeleton-content">
                <div class="skeleton-title"></div>
                <div class="skeleton-subtitle"></div>
                <div class="skeleton-price"></div>
              </div>
            </div>

            <!-- 按钮骨架 -->
            <div class="skeleton-buttons">
              <div class="skeleton-button"></div>
              <div class="skeleton-button"></div>
            </div>
          </div>
        </WoCard>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!showSkeleton && !loading && orderList.length === 0 && finished" class="empty-state">
      <div class="empty-content">
        <img src="../assets/no-data.png" alt="暂无订单" class="empty-image" />
      </div>
    </div>

    <!-- 订单列表 -->
    <van-list v-else v-model:loading="loading" :finished="finished" loading-text="加载中..."  finished-text="没有更多了" @load="onLoad"
      :immediate-check="false">
      <div v-for="order in orderList" :key="order.id" class="order-item">
        <WoCard>
          <!-- 订单头部信息 -->
          <div class="order-header">
            <div class="order-number-container">
              <span class="order-number-text">订单号：{{ order.id }}</span>
              <img src="@/static/images/copy.png" alt="复制" class="copy-icon" @click.stop="copyOrderNumber(order.id)" />
            </div>
            <!-- 待付款订单显示倒计时，其他订单显示状态 -->
            <div v-if="order.orderState === '0' && order.remainingTime > 0" class="countdown-container">
              <span class="countdown-text">剩余</span>
              <span class="countdown-time">{{ formatTime(order.remainingTime) }}</span>
            </div>
            <div v-else class="order-status" :class="getStatusClass(order.orderState)">{{ orderState(order.orderState)
            }}</div>
          </div>

          <!-- 商品列表 -->
          <div class="goods-section">
            <!-- 多商品订单：显示为一个整体 -->
            <GoodsItemCard :key="order.id" :item="order" :image-size="75" :min-height="110" :showActions="true"
              :moreActions="getMoreActions(order)" :itemId="order.id" @click="onDetailClick(order)">
              <template #actions>
                <WoButton v-for="button in getVisibleButtons(order)" :key="button.text"
                  :type="getButtonType(button.color)" size="small" @click.stop="button.handler">
                  {{ button.text }}
                </WoButton>
              </template>
            </GoodsItemCard>
          </div>
        </WoCard>
      </div>
    </van-list>
  </div>
  <Certificate :show="certificate.show" :title="certificate.title" :date="certificate.date" :amt="certificate.amt"
    @close="onCertificateClose" />
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import GoodsItemCard from '@components/Common/GoodsItemCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import Certificate from './Certificate/Certificate.vue'
import useClipboard from 'vue-clipboard3'
import { closeToast, showLoadingToast, showToast } from 'vant'
import orderState from '@/utils/orderState.js'
import { cancelOrder, getOrderList } from '@/api/interface/order.js'
import { getBizCode } from '@/utils/curEnv.js'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { manualConfirmRecv, verifySupplierOrderRepurchased, modOrderListShow, getOrderExpress, repayOrder } from '@/api/interface/order.js'
import { useAlert } from '@/hooks/index.js'
import { formSubmit } from 'commonkit'
import { buyProductCart, buyProductCartSession } from '@/utils/storage.js'
import { fenToYuan } from '@utils/amount.js'
const { toClipboard } = useClipboard()
const $alert = useAlert()
const props = defineProps({
  tabType: {
    type: String,
    required: true
  },
  scrollPosition: {
    type: Number,
    default: 0
  }
})

// 在script setup中添加路由实例
const router = useRouter()

const emit = defineEmits(['update-scroll'])
const contentRef = ref(null)
const loading = ref(false)
const finished = ref(false)
const orderList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalPage = ref(0)
const error = ref(false)
const finishedText = ref('没有更多了')
const isRefreshing = ref(false) // 标识是否正在刷新

// 添加响应式变量（在现有变量定义区域）
const dialogShow = ref(false)
const wapay = ref({
  encryptContent: '',
  wapURL: '',
  bizOrderId: ''
})

// 获取状态样式类
const getStatusClass = (state) => {
  switch (state) {
    case '0': // 待付款
    case '3': // 待发货
    case '5': // 配送中
      return 'status-unpaid'
    case '9': // 已签收
      return 'status-completed'
    default:
      return 'status-completed'
  }
}

// 获取按钮类型
const getButtonType = (color) => {
  switch (color) {
    case 'orange':
      return 'gradient'
    case 'gray':
    default:
      return 'default'
  }
}

// 检查是否显示查看物流按钮
const toShowViewLogistics = (item) => {
  // 根据业务逻辑判断是否显示查看物流按钮
  return item.orderState === '5' || item.orderState === '9'
}

// 检查是否显示香蕉树按钮
const toShowBananaTree = (item) => {
  // 根据业务逻辑判断是否显示香蕉树按钮
  return item.supplierDistriBiz?.distriBizCode === 'banana'
}

// 获取所有按钮（内部方法）
const getAllButtons = (item) => {
  const grayButtons = []
  const orangeButtons = []
  const deleteButton = []

  // 删除订单按钮 (单独存放，确保在更多菜单中)
  if (item.orderState === '2' || item.orderState === '9' || item.orderState === '10') {
    deleteButton.push({
      text: '删除订单',
      color: 'gray',
      handler: () => onDeleteOrder(item)
    })
  }

  if (item.orderState === '0') {
    grayButtons.push({
      text: '取消订单',
      color: 'gray',
      handler: () => onCancelClick(item.id)
    })
  }

  // 查看物流按钮
  if (toShowViewLogistics(item)) {
    grayButtons.push({
      text: '查看物流',
      color: 'gray',
      handler: () => onExpressClick(item)
    })
  }

  // 香蕉树按钮
  if (toShowBananaTree(item)) {
    grayButtons.push({
      text: '我的香蕉树',
      color: 'gray',
      handler: () => onBananaClick()
    })
  }

  // 荣誉证书按钮
  if (item.supplierDistriBiz?.distriBizCode === 'fupin' &&
    (item.orderState === '1' || item.orderState === '3' || item.orderState === '5' || item.orderState === '9')) {
    grayButtons.push({
      text: '荣誉证书',
      color: 'gray',
      handler: () => onCertificateClick(item)
    })
  }

  // 去支付按钮 (待付款状态)
  if (item.orderState === '0') {
    orangeButtons.push({
      text: '去支付',
      color: 'orange',
      handler: () => onPayClick(item.id)
    })
  }

  // 催发货按钮
  if (item.orderState === '3') {
    orangeButtons.push({
      text: '催发货',
      color: 'orange',
      handler: () => onUrgeShipment(item)
    })
  }

  // 确认收货
  if (item.orderState === '5') {
    orangeButtons.push({
      text: '确认收货',
      color: 'orange',
      handler: () => onConfirmReceipt(item)
    })
  }

  // 再次购买按钮
  if (item.orderState === '2' || item.orderState === '9' || item.orderState === '10') {
    orangeButtons.push({
      text: '再次购买',
      color: 'orange',
      handler: () => onBuyClick(item)
    })
  }

  // 按照要求排序：删除按钮(如果总数<=3) -> 灰色按钮 -> 橙色按钮
  const totalButtons = [...deleteButton, ...grayButtons, ...orangeButtons]

  // 如果按钮总数大于3个，确保删除按钮在更多菜单中
  if (totalButtons.length > 3) {
    return [...grayButtons, ...orangeButtons]
  }

  return [...deleteButton, ...grayButtons, ...orangeButtons]
}

// 获取可见按钮（前3个）
const getVisibleButtons = (item) => {
  const allButtons = getAllButtons(item)
  return allButtons.slice(0, 3)
}

// 获取更多操作按钮（第4个及以后）
const getMoreActions = (item) => {
  const grayButtons = []
  const orangeButtons = []
  const deleteButton = []

  if (item.orderState === '2' || item.orderState === '9' || item.orderState === '10') {
    deleteButton.push({
      text: '删除订单',
      color: 'gray',
      handler: () => onDeleteOrder(item)
    })
  }

   if (item.orderState === '0') {
    grayButtons.push({
      text: '取消订单',
      color: 'gray',
      handler: () => onCancelClick(item.id)
    })
  }

  if (toShowViewLogistics(item)) {
    grayButtons.push({
      text: '查看物流',
      color: 'gray',
      handler: () => onExpressClick(item)
    })
  }

  if (toShowBananaTree(item)) {
    grayButtons.push({
      text: '我的香蕉树',
      color: 'gray',
      handler: () => onBananaClick()
    })
  }

  if (item.supplierDistriBiz?.distriBizCode === 'fupin' &&
    (item.orderState === '1' || item.orderState === '3' || item.orderState === '5' || item.orderState === '9')) {
    grayButtons.push({
      text: '荣誉证书',
      color: 'gray',
      handler: () => onCertificateClick(item)
    })
  }

  if (item.orderState === '0') {
    orangeButtons.push({
      text: '去支付',
      color: 'orange',
      handler: () => onPayClick(item.id)
    })
  }

  if (item.orderState === '3') {
    orangeButtons.push({
      text: '催发货',
      color: 'orange',
      handler: () => onUrgeShipment(item)
    })
  }

  if (item.orderState === '5') {
    orangeButtons.push({
      text: '确认收货',
      color: 'orange',
      handler: () => onConfirmReceipt(item)
    })
  }

  if (item.orderState === '2' || item.orderState === '9' || item.orderState === '10') {
    orangeButtons.push({
      text: '再次购买',
      color: 'orange',
      handler: () => onBuyClick(item)
    })
  }

  const totalButtons = [...deleteButton, ...grayButtons, ...orangeButtons]

  // 如果按钮总数大于3个，删除按钮放在更多菜单中，其余按钮从第4个开始
  if (totalButtons.length > 3) {
    const remainingButtons = [...grayButtons, ...orangeButtons].slice(3)
    return [...deleteButton, ...remainingButtons]
  }

  // 如果总数<=3，更多菜单为空
  return []
}

// 订单详情点击处理
const onDetailClick = (order) => {
  router.push({
    path: '/user/order/detail',
    query: {
      orderId: order.id,
      isPay: order.orderState === '0' ? '1' : '2'
    }
  })
}

// 监听标签类型变化，重新加载数据
watch(() => props.tabType, () => {
  // 清除所有倒计时
  clearAllCountdowns()

  // 标签页切换时立即显示骨架屏并重置状态
  isTabSwitching.value = true
  orderList.value = []
  currentPage.value = 1
  finished.value = false
  error.value = false
  loading.value = false // 先设为false，避免与骨架屏逻辑冲突

  // 延迟一帧再加载数据，确保骨架屏能正确显示
  nextTick(() => {
    onLoad()
  })
})
// 标签页切换状态
const isTabSwitching = ref(false)

// 骨架屏显示控制
const showSkeleton = computed(() => {
  // 标签页切换时显示骨架屏，或者首次加载且列表为空时显示骨架屏
  return isTabSwitching.value || (loading.value && orderList.value.length === 0 && !error.value)
})

// 监听滚动事件，更新滚动位置
const handleScroll = () => {
  if (contentRef.value) {
    const scrollTop = contentRef.value.scrollTop || document.documentElement.scrollTop || document.body.scrollTop
    emit('update-scroll', scrollTop)
  }
}

// 复制订单号
const copyOrderNumber = async (orderNumber) => {
  try {
    await toClipboard(orderNumber);
    showToast('复制成功');
  } catch (e) {
    console.error(e);
    showToast('复制失败');
  }
}

// 加载数据
const onLoad = async () => {
  const params = {
    disriBiz: getBizCode('ORDER'),
    orderState: props.tabType,
    pageNum: currentPage.value,
    pageSize: pageSize.value
  }

  showLoadingToast()
  const [err, json] = await getOrderList(params)
  closeToast()

  if (!err) {
    currentPage.value++
    loading.value = false

    if (json?.list.length > 0) {
      finishedText.value = '没有更多了'
    }

    // 如果没有数据，标记为完成
    if (json && json?.list.length <= 0) {
      finished.value = true
      // 数据加载完成，隐藏标签页切换骨架屏
      isTabSwitching.value = false
      return
    }

    const newOrders = json.list || []
    const expandedOrders = []
    newOrders.forEach(order => {
      // 如果有supplierOrderList，需要合并所有供应商订单的商品信息
      if (order.supplierOrderList && order.supplierOrderList.length > 0) {
        // 合并所有供应商订单的skuNumInfoList
        let mergedSkuNumInfoList = []
        let totalPrice = 0

        order.supplierOrderList.forEach(supplierOrder => {
          if (supplierOrder.skuNumInfoList && supplierOrder.skuNumInfoList.length > 0) {
            mergedSkuNumInfoList = [...mergedSkuNumInfoList, ...supplierOrder.skuNumInfoList]
          }
          // 累加价格
          if (supplierOrder.price) {
            totalPrice += parseFloat(supplierOrder.price) || 0
          }
        })

        // 将合并后的商品信息添加到主订单中
        const mergedOrder = {
          ...order,
          skuNumInfoList: mergedSkuNumInfoList,
          price: totalPrice > 0 ? totalPrice.toString() : order.price,
          totalPrice: totalPrice > 0 ? totalPrice.toString() : order.totalPrice
        }

        expandedOrders.push(mergedOrder)
      } else {
        // 没有supplierOrderList，直接添加原订单
        expandedOrders.push(order)
      }
    })
    // 如果是刷新操作，替换数据；否则追加数据
    if (isRefreshing.value) {
      orderList.value = expandedOrders
      isRefreshing.value = false // 重置刷新标识
    } else if (currentPage.value === 2) {
      // 第一页数据，直接替换
      orderList.value = expandedOrders
    } else {
      // 后续页面数据，追加到现有数据
      orderList.value = [...orderList.value, ...expandedOrders]
    }
    // 为每个订单添加 showPopover 属性
    orderList.value.forEach(item => {
      if (!Object.prototype.hasOwnProperty.call(item, 'showPopover')) {
        item.showPopover = false
      }
    })

    // 为新加载的待付款订单计算剩余时间并启动倒计时
    nextTick(() => {
      expandedOrders.forEach((order) => {
        if (order.orderState === '0') {
          // 计算剩余时间
          const createTime = order.createTime || order.orderDate
          if (createTime) {
            const now = Date.now()
            const orderTime = new Date(createTime).getTime()
            const elapsed = now - orderTime
            const remaining = PAYMENT_TIMEOUT - elapsed

            // 找到订单在完整列表中的索引
            const orderIndex = orderList.value.findIndex(o => o.id === order.id)
            if (orderIndex !== -1) {
              // 设置剩余时间
              orderList.value[orderIndex].remainingTime = Math.max(0, remaining)

              // 如果还有剩余时间，启动倒计时
              if (orderList.value[orderIndex].remainingTime > 0) {
                startCountdown(orderList.value[orderIndex])
              } else {
                // 时间已过期，标记为已取消状态
                orderList.value[orderIndex].orderState = '2'
                orderList.value[orderIndex].remainingTime = 0
              }
            }
          }
        }
      })
    })

    // 当前列表有多少页
    totalPage.value = json.totalPage
    if (currentPage.value > totalPage.value) {
      finished.value = true
    }

    // 数据加载完成，隐藏标签页切换骨架屏
    isTabSwitching.value = false

    // 恢复滚动位置
    if (currentPage.value === 2 && props.scrollPosition > 0) {
      nextTick(() => {
        if (contentRef.value) {
          contentRef.value.scrollTop = props.scrollPosition
        }
      })
    }
  } else {
    error.value = true
    loading.value = false
    finished.value = true
    // 加载失败时也要隐藏标签页切换骨架屏
    isTabSwitching.value = false
  }
}

// 倒计时相关
const countdownTimers = ref(new Map()) // 存储每个订单的定时器
const PAYMENT_TIMEOUT = 30 * 60 * 1000 // 30分钟支付超时时间

// 格式化时间显示
const formatTime = (milliseconds) => {
  if (milliseconds <= 0) return '00:00:00'

  const totalSeconds = Math.floor(milliseconds / 1000)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

// 启动单个订单的倒计时
const startCountdown = (order) => {
  if (order.orderState !== '0') return

  const createTime = order.createTime || order.orderDate
  if (!createTime) return

  // 清除已存在的定时器
  if (countdownTimers.value.has(order.id)) {
    clearInterval(countdownTimers.value.get(order.id))
  }

  const updateCountdown = () => {
    const now = Date.now()
    const orderTime = new Date(createTime).getTime()
    const elapsed = now - orderTime
    const remaining = PAYMENT_TIMEOUT - elapsed

    if (remaining <= 0) {
      // 时间到期，更新订单状态为已取消
      // 找到订单在列表中的索引并更新
      const orderIndex = orderList.value.findIndex(o => o.id === order.id)
      if (orderIndex !== -1) {
        orderList.value[orderIndex].remainingTime = 0
        orderList.value[orderIndex].orderState = '2' // 已取消
      }

      // 清除定时器
      clearInterval(countdownTimers.value.get(order.id))
      countdownTimers.value.delete(order.id)

      // 这里可以调用后端接口更新订单状态
      // updateOrderStatus(order.id, '2')
    } else {
      // 找到订单在列表中的索引并更新剩余时间
      const orderIndex = orderList.value.findIndex(o => o.id === order.id)
      if (orderIndex !== -1) {
        orderList.value[orderIndex].remainingTime = remaining
      }
    }
  }

  // 立即执行一次
  updateCountdown()

  // 设置定时器，每秒更新一次
  const timer = setInterval(updateCountdown, 1000)
  countdownTimers.value.set(order.id, timer)
}

// 清除单个订单的倒计时
const clearCountdown = (orderId) => {
  if (countdownTimers.value.has(orderId)) {
    clearInterval(countdownTimers.value.get(orderId))
    countdownTimers.value.delete(orderId)
  }
}

// 清除所有倒计时
const clearAllCountdowns = () => {
  countdownTimers.value.forEach(timer => clearInterval(timer))
  countdownTimers.value.clear()
}

// 重置数据
const resetData = () => {
  // 清除所有定时器
  clearAllCountdowns()
  orderList.value = []
  currentPage.value = 1
  finished.value = false
  error.value = false
  loading.value = false
  isRefreshing.value = false
}

const onCancelClick = async (bizOrderId) => {
  const cancelOrderFn = async () => {
    showLoadingToast()
    const [err] = await cancelOrder(bizOrderId)
    closeToast()
    if (!err) {
      // 清理被取消订单的定时器
      const canceledOrder = orderList.value.find(order => order.bizOrderId === bizOrderId)
      if (canceledOrder) {
        clearCountdown(canceledOrder.id)
      }
      resetData()
      await onLoad()
    } else {
      showToast(err.msg)
    }
  }

  await $alert({
    title: '',
    message: '取消后将无法恢复，您确定要取消订单吗？',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    showCancelButton: true,
    onConfirmCallback: async () => {
      await cancelOrderFn()
    }
  })
}

// 按钮处理方法
const onDeleteOrder = async (item) => {
  const cancelOrderFn = async () => {
    showLoadingToast()

    try {
      // 如果有supplierOrderList，需要对每个供应商订单分别删除
      if (item.supplierOrderList && item.supplierOrderList.length > 0) {
        const deletePromises = item.supplierOrderList.map(supplierOrder => {
          const params = {
            supplierOrderId: supplierOrder.id,
            isDelete: 1
          }
          return modOrderListShow(params)
        })

        const results = await Promise.all(deletePromises)
        const hasError = results.some(([err]) => err)

        if (hasError) {
          const errorResult = results.find(([err]) => err)
          throw new Error(errorResult[0].msg)
        }
      } else {
        // 没有supplierOrderList，直接删除主订单
        const params = {
          supplierOrderId: item.id,
          isDelete: 1
        }
        const [err] = await modOrderListShow(params)
        if (err) {
          throw new Error(err.msg)
        }
      }

      closeToast()

      // 清理被删除订单的定时器
      clearCountdown(item.id)

      // 找到订单在 orderList 中的索引
      const index = orderList.value.findIndex(order => order.id === item.id)
      if (index !== -1) {
        // 标记为正在删除以触发动画
        orderList.value[index].isDeleting = true
        setTimeout(() => {
          // 从 orderList 中删除该订单
          orderList.value.splice(index, 1)
          if (orderList.value.length === 0) {
            finishedText.value = ''
          }
        }, 500) // 动画持续时间
      }
    } catch (error) {
      closeToast()
      showToast(error.message || '删除失败')
    }
  }

  try {
    await $alert({
      title: '',
      message: '确认删除该订单？',
      confirmButtonText: '确定',
      showCancelButton: true,
      cancelButtonText: '取消',
      onConfirmCallback: async () => {
        await cancelOrderFn()
      }
    })
  } catch (error) {
    // 用户点击取消或关闭弹窗
    console.log('用户取消删除操作')
  }
}

const onExpressClick = async (item) => {
  const { id, orderDate } = item
  const now = dayjs()
  const orderDateDayjs = dayjs(orderDate)
  const endTimeSub180 = now.subtract(12, 'month')
  const isWithinScope = orderDateDayjs.isBefore(endTimeSub180, 'minute')

  if (isWithinScope) {
    showToast('物流信息已失效 ！')
    return
  }

  try {
    const [err, orderExpress] = await getOrderExpress(id)

    if (err) {
      showToast('查询物流信息失败')
      return
    }

    const { orderPackageList } = orderExpress
    if (orderPackageList && orderPackageList.length > 0) {
      router.push({
        name: 'user-order-entry-express',
        params: {
          orderExpress: orderExpress
        },
        query: {
          orderId: id
        }
      })
      return
    }

    showToast('物流信息已失效 ！')
  } catch (error) {
    console.error('查询物流信息失败:', error)
    showToast('查询物流信息失败')
  }
}

const onBananaClick = () => {
  router.push('/fpHome/banana-tree')
}

const certificate = ref({
  show: false,
  title: '',
  date: new Date(),
  amt: ''
})

// 扶贫荣誉证书显示
const onCertificateClick = (item) => {
  certificate.value.date = new Date(item.orderDate.replace(/-/g, '/'))
  certificate.value.title = '尊敬的沃钱包用户：'
  certificate.value.amt = fenToYuan(item.paymentDetail.actualPayAmount)
  certificate.value.show = true
}

// 扶贫荣誉证书关闭
const onCertificateClose = () => {
  certificate.value.show = false
}

const onPayClick = async (bizOrderId) => {
  showLoadingToast()
  try {
    const [res, json] = await repayOrder(bizOrderId)
    closeToast()

    if (res.code === '0000') {
      if (getBizCode() === 'fupin' && json.isNeedCompanyInsert === 'true') {
        dialogShow.value = true
        wapay.value.encryptContent = json.encryptContent
        wapay.value.wapURL = json.wapURL
        wapay.value.bizOrderId = json.storeOrderId
      } else {
        formSubmit(json.wapURL, { param: json.encryptContent })
      }
    } else if (res.code === '2091070302' && res.data && res.data.length > 0) {
      // 订单中有下架 or 库存不足 or 无货的商品
      if (json.some(info => info.state === '2')) {
        showToast('您的订单中有商品已下架')
      } else if (json.some(info => info.state === '3')) {
        showToast('您的订单中有无货商品')
      } else if (json.some(info => info.state === '4')) {
        showToast('您的订单中有商品库存不足')
      }
    } else {
      showToast(res.msg)
    }
  } catch (error) {
    closeToast()
    console.error('支付失败:', error)
    showToast('支付失败，请重试')
  }
}

const onUrgeShipment = (item) => {
  const { orderDate } = item
  const targetDate = dayjs(orderDate)
  const now = dayjs()
  // 计算两个时间之间的差异（毫秒）
  const diff = now.diff(targetDate, 'millisecond')
  // 将毫秒差异转换成小时
  const diffInHours = diff / (1000 * 60 * 60)
  const isWithin48Hours = Math.abs(diffInHours) <= 48

  if (isWithin48Hours) {
    const dateAdd48 = targetDate.add(48, 'hour')
    const formattedDate = dateAdd48.format('M月DD日')
    $alert({
      messageHtml: `<div>您的商品目前处于正常配送时效内，商家将于<span style="color:#FF780A;">${formattedDate}</span>前发货，请您耐心等待。</div>`,
      confirmButtonText: '确定',
      allowHtml: true,
      messageAlign: 'center'
    })
  } else {
    $alert({
      message: '给您带来的不便深感抱歉，已为您提醒商家发货，请您耐心等待。',
      confirmButtonText: '确定',
      messageAlign: 'center'
    })
  }
}

const onConfirmReceipt = async (item) => {
  try {
    await $alert({
      title: '确认收货',
      message: '确认收到商品了吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      messageAlign: 'center'
    })

    // 用户确认后执行收货逻辑
    showLoadingToast()

    try {
      // 如果有supplierOrderList，需要对每个供应商订单分别确认收货
      if (item.supplierOrderList && item.supplierOrderList.length > 0) {
        const confirmPromises = item.supplierOrderList.map(supplierOrder => {
          return manualConfirmRecv({ supplierOrderId: supplierOrder.id })
        })

        const results = await Promise.all(confirmPromises)
        const hasError = results.some(([err]) => err)

        if (hasError) {
          const errorResult = results.find(([err]) => err)
          throw new Error(errorResult[0].msg)
        }
      } else {
        // 没有supplierOrderList，直接确认主订单
        const [err] = await manualConfirmRecv({ supplierOrderId: item.id })
        if (err) {
          throw new Error(err.msg)
        }
      }

      closeToast()

      // 确认收货成功，重新加载数据
      resetData()
      onLoad()
    } catch (error) {
      closeToast()
      showToast(error.message || '操作失败')
    }
  } catch (error) {
    // 用户取消操作
    console.log('用户取消确认收货')
  }
}

const onBuyClick = async (orderInfo) => {
  const bizCode = getBizCode('ORDER')

  showLoadingToast()

  try {
    let allValidGoodsList = []
    let totalSkuCount = 0

    // 如果有supplierOrderList，需要对每个供应商订单分别检查
    if (orderInfo.supplierOrderList && orderInfo.supplierOrderList.length > 0) {
      const verifyPromises = orderInfo.supplierOrderList.map(supplierOrder => {
        return verifySupplierOrderRepurchased({
          bizCode,
          supplierOrderId: supplierOrder.id
        })
      })

      const results = await Promise.all(verifyPromises)

      // 合并所有供应商订单的有效商品
      results.forEach(([err, res], index) => {
        if (!err && res && res.validGoodsList) {
          allValidGoodsList = [...allValidGoodsList, ...res.validGoodsList]
        }
        // 累计总的sku数量
        const supplierOrder = orderInfo.supplierOrderList[index]
        if (supplierOrder.skuNumInfoList) {
          totalSkuCount += supplierOrder.skuNumInfoList.length
        }
      })
    } else {
      // 没有supplierOrderList，直接检查主订单
      const [err, res] = await verifySupplierOrderRepurchased({
        bizCode,
        supplierOrderId: orderInfo.id
      })

      if (!err && res) {
        allValidGoodsList = res.validGoodsList || []
      }
      totalSkuCount = orderInfo.skuNumInfoList ? orderInfo.skuNumInfoList.length : 0
    }

    closeToast()

    if (allValidGoodsList.length === 0) {
      showToast('订单中的商品都卖光了，在看看其他商品吧~')
      return
    }

    if (allValidGoodsList.length > 0 && totalSkuCount === allValidGoodsList.length) {
      buyProductCart.set(allValidGoodsList)
      buyProductCartSession.set(allValidGoodsList)
      router.push('/orderconfirm')
    }

    if (allValidGoodsList.length > 0 && totalSkuCount > allValidGoodsList.length) {
      buyProductCart.set(allValidGoodsList)
      buyProductCartSession.set(allValidGoodsList)

      try {
        await $alert({
          title: '',
          message: '部分商品无货或已下架无法购买!',
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })

        // 用户点击确定
        router.push('/orderconfirm')
      } catch (error) {
        // 用户点击取消或关闭弹窗
        console.log('用户取消操作')
      }
    }
  } catch (error) {
    closeToast()
    console.error('再次购买检查失败:', error)
    showToast('操作失败，请重试')
  }
}

// 启动所有待付款订单的倒计时
const startAllCountdowns = () => {
  orderList.value.forEach((order, index) => {
    if (order.orderState === '0') {
      const createTime = order.createTime || order.orderDate
      if (createTime) {
        const now = Date.now()
        const orderTime = new Date(createTime).getTime()
        const elapsed = now - orderTime
        const remaining = PAYMENT_TIMEOUT - elapsed

        // 设置剩余时间
        orderList.value[index].remainingTime = Math.max(0, remaining)

        // 如果还有剩余时间，启动倒计时
        if (orderList.value[index].remainingTime > 0) {
          startCountdown(orderList.value[index])
        } else {
          // 时间已过期，标记为已取消状态
          orderList.value[index].orderState = '2'
          orderList.value[index].remainingTime = 0
        }
      }
    }
  })
}

// 页面可见性变化处理
const handleVisibilityChange = () => {
  if (document.hidden) {
    // 页面不可见时暂停所有定时器
    clearAllCountdowns()
  } else {
    // 页面可见时重新启动倒计时
    nextTick(() => {
      startAllCountdowns()
    })
  }
}

// 组件挂载时
onMounted(() => {
  // 添加滚动事件监听
  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', handleScroll)
  } else {
    window.addEventListener('scroll', handleScroll)
  }

  // 添加页面可见性变化监听
  document.addEventListener('visibilitychange', handleVisibilityChange)

  // 设置初始加载状态并加载数据
  loading.value = true
  onLoad()

  // 为已存在的订单启动倒计时
  nextTick(() => {
    startAllCountdowns()
  })
})

// 刷新数据方法（供父组件调用）
const refreshData = async () => {
  try {
    // 设置刷新标识
    isRefreshing.value = true
    // 清除所有定时器
    clearAllCountdowns()
    // 重置数据状态，但保持刷新标识
    orderList.value = []
    currentPage.value = 1
    finished.value = false
    error.value = false
    loading.value = true
    // 重新加载数据
    await onLoad()
  } catch (error) {
    console.error('刷新数据失败:', error)
    isRefreshing.value = false
    throw error
  }
}

// 暴露方法给父组件
defineExpose({
  refreshData
})

// 组件卸载时
onUnmounted(() => {
  // 移除滚动事件监听
  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', handleScroll)
  } else {
    window.removeEventListener('scroll', handleScroll)
  }

  // 移除页面可见性变化监听
  document.removeEventListener('visibilitychange', handleVisibilityChange)

  // 清除所有定时器
  clearAllCountdowns()
})

</script>

<style scoped lang="less">
.order-tab-content {
  min-height: calc(100vh - 134px); // 减去搜索头部、tab头部高度
  padding: 10px;
}

.order-item {
  margin-bottom: 10px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 11px;

  .order-shop {
    font-size: @font-size-14;
    font-weight: bold;
    color: @text-color-primary;
  }

  .order-number-container {
    display: flex;
    align-items: center;
    margin-right: 15px;
    width: 100%;
    overflow: hidden;

    .order-number-text {
      font-size: @font-size-11;
      color: @text-color-secondary;
      margin-right: 3px;
      .ellipsis()
    }

    .copy-icon {
      width: 10px;
      height: 10px;
      cursor: pointer;
    }
  }


  .countdown-container {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    color: @theme-color;

    .countdown-text {
      font-size: @font-size-14;
      font-weight: @font-weight-600;
      margin-right: 4px;
      color: @theme-color;
    }

    .countdown-time {
      font-size: @font-size-14;
      font-weight: @font-weight-600;
      color: @theme-color;
    }
  }


  .order-status {
    flex-shrink: 0;
    font-size: @font-size-14;
    font-weight: @font-weight-600;

    &.status-unpaid {
      color: @theme-color;
    }

    &.status-unshipped {
      color: #2196f3;
    }

    &.status-shipped {
      color: #4caf50;
    }

    &.status-completed {
      color: @text-color-secondary;
    }
  }
}

.goods-section {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

// 骨架屏样式
.skeleton-container {
  .skeleton-order {
    padding: 0;
  }

  .skeleton-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;

    .skeleton-order-number {
      width: 120px;
      height: 12px;
      background: #f0f0f0;
      border-radius: 6px;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }

    .skeleton-status {
      width: 60px;
      height: 16px;
      background: #f0f0f0;
      border-radius: 8px;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }
  }

  .skeleton-goods {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;

    .skeleton-image {
      width: 75px;
      height: 75px;
      background: #f0f0f0;
      border-radius: 8px;
      margin-right: 12px;
      flex-shrink: 0;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }

    .skeleton-content {
      flex: 1;
      min-height: 75px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .skeleton-title {
        width: 80%;
        height: 16px;
        background: #f0f0f0;
        border-radius: 8px;
        margin-bottom: 8px;
        animation: skeleton-loading 1.5s ease-in-out infinite;
      }

      .skeleton-subtitle {
        width: 60%;
        height: 12px;
        background: #f0f0f0;
        border-radius: 6px;
        margin-bottom: 8px;
        animation: skeleton-loading 1.5s ease-in-out infinite;
      }

      .skeleton-price {
        width: 40%;
        height: 14px;
        background: #f0f0f0;
        border-radius: 7px;
        animation: skeleton-loading 1.5s ease-in-out infinite;
      }
    }
  }

  .skeleton-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    .skeleton-button {
      width: 60px;
      height: 28px;
      background: #f0f0f0;
      border-radius: 14px;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }
  }
}

@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.6;
  }

  100% {
    opacity: 1;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;

  .empty-content {
    text-align: center;

    .empty-image {
      width: 160px;
      height: 140px;
      margin-bottom: 16px;
    }

    .empty-text {
      font-size: @font-size-14;
      color: @text-color-secondary;
      margin: 0;
    }
  }
}
</style>
