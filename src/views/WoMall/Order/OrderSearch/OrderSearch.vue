<template>
  <div class="order-search">
    <SearchHeader v-model="searchKeyword" placeholder="搜索我的订单" @search="handleSearch" class="search-header">
    </SearchHeader>
    <div class="order-search-content" ref="contentRef">
      <!-- 骨架屏 -->
      <div v-if="showSkeleton" class="skeleton-container">
        <div v-for="i in 3" :key="`skeleton-${i}`" class="order-item">
          <WoCard>
            <div class="skeleton-order">
              <!-- 订单头部骨架 -->
              <div class="skeleton-header">
                <div class="skeleton-order-number"></div>
                <div class="skeleton-status"></div>
              </div>

              <!-- 商品信息骨架 -->
              <div class="skeleton-goods">
                <div class="skeleton-image"></div>
                <div class="skeleton-content">
                  <div class="skeleton-title"></div>
                  <div class="skeleton-subtitle"></div>
                  <div class="skeleton-price"></div>
                </div>
              </div>

              <!-- 按钮骨架 -->
              <div class="skeleton-buttons">
                <div class="skeleton-button"></div>
                <div class="skeleton-button"></div>
              </div>
            </div>
          </WoCard>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!showSkeleton && !loading && orderList.length === 0 && finished" class="empty-state">
        <div class="empty-content">
          <img src="../assets/no-data.png" alt="暂无订单" class="empty-image" />
        </div>
      </div>

      <!-- 订单列表 -->
      <van-list v-if="!showSkeleton" v-model:loading="loading" :finished="finished" :finished-text="orderList.length > 0 ? '没有更多了' : ''" @load="onLoad"
        :immediate-check="false">
        <div v-for="order in orderList" :key="order.id" class="order-item">
          <WoCard>
            <!-- 订单头部信息 -->
            <div class="order-header">
              <div class="order-number-container">
                <span class="order-number-text">订单号：{{ order.id }}</span>
                <img src="@/static/images/copy.png" alt="复制" class="copy-icon"
                  @click.stop="copyOrderNumber(order.id)" />
              </div>
              <!-- 待付款订单显示倒计时，其他订单显示状态 -->
              <div v-if="order.orderState === '0' && order.remainingTime > 0" class="countdown-container">
                <span class="countdown-text">剩余</span>
                <span class="countdown-time">{{ formatTime(order.remainingTime) }}</span>
              </div>
              <div v-else class="order-status" :class="getStatusClass(order.orderState)">{{ orderState(order.orderState)
                }}</div>
            </div>

            <!-- 商品列表 -->
            <div class="goods-section">
              <!-- 多商品订单：显示为一个整体 -->
              <GoodsItemCard :key="order.id" :item="order" :image-size="75" :min-height="110" :showActions="true"
                :moreActions="getMoreActions(order)" :itemId="order.id">
                <template #actions>
                  <WoButton v-for="button in getVisibleButtons(order)" :key="button.text"
                    :type="getButtonType(button.color)" size="small" @click="button.handler">
                    {{ button.text }}
                  </WoButton>
                </template>
              </GoodsItemCard>
            </div>
          </WoCard>
        </div>
      </van-list>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick, onActivated, onDeactivated } from 'vue'
import SearchHeader from "@components/Common/SearchHeader.vue";
import WoCard from '@components/WoElementCom/WoCard.vue'
import GoodsItemCard from '@components/Common/GoodsItemCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import useClipboard from 'vue-clipboard3'
import { closeToast, showLoadingToast, showToast } from 'vant'
import orderState from '@/utils/orderState.js'
import { useAlert } from '@/hooks/index.js'
import { getOrderSearchList } from '@/api/interface/order.js'
import { getBizCode } from '@/utils/curEnv.js'
import { useRoute,useRouter } from 'vue-router'
const { toClipboard } = useClipboard()
const $alert = useAlert()
const route = useRoute()
const router = useRouter()
// 搜索关键词
const searchKeyword = ref('')

// 保存标签页的滚动位置
const scrollPositions = ref({
  all: 0
})

// 搜索状态控制
const isSearching = ref(false)

// 更新滚动位置
const updateScrollPosition = (position) => {
  scrollPositions.value.all = position
}
const contentRef = ref(null)
const loading = ref(false)
const finished = ref(false)
const orderList = ref([])
const page = ref(1)
const pageSize = ref(10)
const showSkeleton = ref(false)

// 监听滚动事件，更新滚动位置
const handleScroll = () => {
  if (contentRef.value) {
    const scrollTop = contentRef.value.scrollTop || document.documentElement.scrollTop || document.body.scrollTop
    updateScrollPosition(scrollTop)
  }
}

// 复制订单号
const copyOrderNumber = async (orderNumber) => {
  try {
    await toClipboard(orderNumber);
    showToast('复制成功');
  } catch (e) {
    console.error(e);
    showToast('复制失败');
  }
}

// 搜索处理函数初始化
// 这个函数将在下面重新定义

// 加载数据
const onLoad = async () => {
  // 首次加载显示骨架屏
  if (page.value === 1 && orderList.value.length === 0) {
    showSkeleton.value = true
  } else {
    loading.value = true
  }

  const params = {
    disriBiz: getBizCode('ORDER'),
    searchContent: searchKeyword.value,
    pageNum: page.value,
    pageSize: pageSize.value
  }

  const [err, json] = await getOrderSearchList(params)

  if (!err) {
    page.value++
    loading.value = false
    showSkeleton.value = false

    if (json?.list.length > 0) {
      // 没有更多数据时的提示文本已在模板中设置
    }

    // 如果没有数据，标记为完成
    if (json && json?.list.length <= 0) {
      loading.value = false
      finished.value = true
      // 数据加载完成，隐藏搜索骨架屏
      isSearching.value = false
      return
    }

    const newOrders = json.list || []
    orderList.value = [...orderList.value, ...newOrders]

    // 为每个订单添加 showPopover 属性
    orderList.value.forEach(item => {
      if (!item.hasOwnProperty('showPopover')) {
        item.showPopover = false
      }
    })

    // 为新加载的待付款订单启动倒计时
    newOrders.forEach(order => {
      if (order.orderState === '0' && order.createTime) {
        startCountdown(order)
      }
    })

    // 当前列表有多少页
    const totalPage = json.totalPage
    if (page.value > totalPage) {
      loading.value = true
      finished.value = true
    }

    // 数据加载完成，隐藏骨架屏
    showSkeleton.value = false

    // 恢复滚动位置
    if (page.value === 2 && scrollPositions.value.all > 0) {
      nextTick(() => {
        if (contentRef.value) {
          contentRef.value.scrollTop = scrollPositions.value.all
        }
      })
    }
  } else {
    loading.value = false
    finished.value = true
    // 加载失败时也要隐藏骨架屏
    showSkeleton.value = false
    showToast('搜索失败，请重试')
  }
}

// 搜索处理函数
// 搜索处理函数
const handleSearch = () => {
  // 实现搜索逻辑
  console.log('搜索关键词:', searchKeyword.value)
  if(!searchKeyword.value) {
    showToast('请输入搜索关键词')
    return
  }

  // 构建新的查询参数
  const newQuery = {
    ...route.query, // 保留现有的查询参数如 _t 和 distri_biz_code
    keyword: searchKeyword.value // 添加搜索关键词到路由参数
  }

  // 更新路由参数，保持原有路径和其他查询参数
  router.replace({
    path: route.path,
    query: newQuery
  })

  // 设置搜索状态，显示骨架屏
  isSearching.value = true
  showSkeleton.value = true

  // 重置数据和分页
  orderList.value = []
  page.value = 1
  finished.value = false
  loading.value = false

  // 清除所有倒计时
  clearAllCountdowns()

  // 重新加载数据
  nextTick(() => {
    onLoad()
  })
}

// 倒计时相关
const countdownTimers = ref(new Map()) // 存储每个订单的定时器
const PAYMENT_TIMEOUT = 30 * 60 * 1000 // 30分钟支付超时时间

// 格式化时间显示
const formatTime = (milliseconds) => {
  if (milliseconds <= 0) return '00:00:00'

  const totalSeconds = Math.floor(milliseconds / 1000)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const seconds = totalSeconds % 60

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

// 启动单个订单的倒计时
const startCountdown = (order) => {
  if (order.orderState !== '0' || !order.createTime) return

  // 清除已存在的定时器
  if (countdownTimers.value.has(order.id)) {
    clearInterval(countdownTimers.value.get(order.id))
  }

  const updateCountdown = () => {
    const now = Date.now()
    const createTime = new Date(order.createTime).getTime()
    const elapsed = now - createTime
    const remaining = PAYMENT_TIMEOUT - elapsed

    if (remaining <= 0) {
      // 时间到期，更新订单状态为已取消
      Object.assign(order, {
        remainingTime: 0,
        orderState: '2' // 已取消
      })

      // 清除定时器
      clearInterval(countdownTimers.value.get(order.id))
      countdownTimers.value.delete(order.id)
    } else {
      // 使用响应式更新剩余时间
      order.remainingTime = remaining
      nextTick(() => {
        // 确保DOM更新
      })
    }
  }

  // 立即执行一次
  updateCountdown()

  // 设置定时器，每秒更新一次
  const timer = setInterval(updateCountdown, 1000)
  countdownTimers.value.set(order.id, timer)
}

// 清除所有倒计时
const clearAllCountdowns = () => {
  countdownTimers.value.forEach(timer => clearInterval(timer))
  countdownTimers.value.clear()
}

// 获取状态样式类
const getStatusClass = (state) => {
  switch (state) {
    case '0': // 待付款
    case '3': // 待发货
    case '5': // 配送中
      return 'status-unpaid'
    case '9': // 已签收
      return 'status-completed'
    default:
      return 'status-unpaid'
  }
}

// 获取按钮类型
const getButtonType = (color) => {
  switch (color) {
    case 'orange':
      return 'primary'
    case 'gray':
    default:
      return 'default'
  }
}

// 检查是否显示查看物流按钮
const toShowViewLogistics = (item) => {
  return item.orderState === '5' || item.orderState === '9'
}

// 检查是否显示香蕉树按钮
const toShowBananaTree = (item) => {
  return item.supplierDistriBiz?.distriBizCode === 'banana'
}

// 获取所有按钮（内部方法）
const getAllButtons = (item) => {
  const grayButtons = []
  const orangeButtons = []
  const deleteButton = []

  // 删除订单按钮
  if (item.orderState === '2' || item.orderState === '9' || item.orderState === '10') {
    deleteButton.push({
      text: '删除订单',
      color: 'gray',
      handler: () => onDeleteOrder(item)
    })
  }

  // 查看物流按钮
  if (toShowViewLogistics(item)) {
    grayButtons.push({
      text: '查看物流',
      color: 'gray',
      handler: () => onExpressClick(item)
    })
  }

  // 香蕉树按钮
  if (toShowBananaTree(item)) {
    grayButtons.push({
      text: '我的香蕉树',
      color: 'gray',
      handler: () => onBananaClick()
    })
  }

  // 荣誉证书按钮
  if (item.supplierDistriBiz?.distriBizCode === 'fupin' &&
    (item.orderState === '1' || item.orderState === '3' || item.orderState === '5' || item.orderState === '9')) {
    grayButtons.push({
      text: '荣誉证书',
      color: 'gray',
      handler: () => onCertificateClick(item)
    })
  }

  // 去支付按钮
  if (item.orderState === '0') {
    orangeButtons.push({
      text: '去支付',
      color: 'orange',
      handler: () => onPayClick(item)
    })
  }

  // 催发货按钮
  if (item.orderState === '3') {
    orangeButtons.push({
      text: '催发货',
      color: 'orange',
      handler: () => onUrgeShipment(item)
    })
  }

  // 确认收货
  if (item.orderState === '5') {
    orangeButtons.push({
      text: '确认收货',
      color: 'orange',
      handler: () => onConfirmReceipt(item)
    })
  }

  // 再次购买按钮
  if (item.orderState === '2' || item.orderState === '9' || item.orderState === '10') {
    orangeButtons.push({
      text: '再次购买',
      color: 'orange',
      handler: () => onBuyClick(item)
    })
  }

  const totalButtons = [...deleteButton, ...grayButtons, ...orangeButtons]

  if (totalButtons.length > 3) {
    return [...grayButtons, ...orangeButtons]
  }

  return [...deleteButton, ...grayButtons, ...orangeButtons]
}

// 获取可见按钮（前3个）
const getVisibleButtons = (item) => {
  const allButtons = getAllButtons(item)
  return allButtons.slice(0, 3)
}

// 获取更多操作按钮（第4个及以后）
const getMoreActions = (item) => {
  const grayButtons = []
  const orangeButtons = []
  const deleteButton = []

  if (item.orderState === '2' || item.orderState === '9' || item.orderState === '10') {
    deleteButton.push({
      text: '删除订单',
      color: 'gray',
      handler: () => onDeleteOrder(item)
    })
  }

  if (toShowViewLogistics(item)) {
    grayButtons.push({
      text: '查看物流',
      color: 'gray',
      handler: () => onExpressClick(item)
    })
  }

  if (toShowBananaTree(item)) {
    grayButtons.push({
      text: '我的香蕉树',
      color: 'gray',
      handler: () => onBananaClick()
    })
  }

  if (item.supplierDistriBiz?.distriBizCode === 'fupin' &&
    (item.orderState === '1' || item.orderState === '3' || item.orderState === '5' || item.orderState === '9')) {
    grayButtons.push({
      text: '荣誉证书',
      color: 'gray',
      handler: () => onCertificateClick(item)
    })
  }

  if (item.orderState === '0') {
    orangeButtons.push({
      text: '去支付',
      color: 'orange',
      handler: () => onPayClick(item)
    })
  }

  if (item.orderState === '3') {
    orangeButtons.push({
      text: '催发货',
      color: 'orange',
      handler: () => onUrgeShipment(item)
    })
  }

  if (item.orderState === '5') {
    orangeButtons.push({
      text: '确认收货',
      color: 'orange',
      handler: () => onConfirmReceipt(item)
    })
  }

  if (item.orderState === '2' || item.orderState === '9' || item.orderState === '10') {
    orangeButtons.push({
      text: '再次购买',
      color: 'orange',
      handler: () => onBuyClick(item)
    })
  }

  const totalButtons = [...deleteButton, ...grayButtons, ...orangeButtons]

  if (totalButtons.length > 3) {
    const remainingButtons = [...grayButtons, ...orangeButtons].slice(3)
    return [...deleteButton, ...remainingButtons]
  }

  return []
}

// 按钮处理方法
const onDeleteOrder = async (order) => {
  try {
    await $alert({
      title: '',
      message: '确认删除该订单？',
      confirmButtonText: '确定',
      showCancelButton: true,
      cancelButtonText: '取消',
      onConfirmCallback: async () => {
        showLoadingToast()
        // 模拟删除操作
        setTimeout(() => {
          closeToast()
          const index = orderList.value.findIndex(item => item.id === order.id)
          if (index !== -1) {
            orderList.value.splice(index, 1)
          }
          showToast('删除成功')
        }, 1000)
      }
    })
  } catch (error) {
    console.log('用户取消删除操作')
  }
}

const onExpressClick = (item) => {
  console.log('查看物流:', item)
  showToast('查看物流功能')
}

const onBananaClick = () => {
  console.log('我的香蕉树')
  showToast('我的香蕉树功能')
}

const onCertificateClick = (item) => {
  console.log('荣誉证书:', item)
  showToast('荣誉证书功能')
}

const onPayClick = (item) => {
  console.log('去支付:', item)
  showToast('去支付功能')
}

const onUrgeShipment = (item) => {
  console.log('催发货:', item)
  showToast('催发货功能')
}

const onConfirmReceipt = async (item) => {
  try {
    await $alert({
      title: '确认收货',
      message: '确认收到商品了吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      messageAlign: 'center'
    })

    showLoadingToast()
    setTimeout(() => {
      closeToast()
      showToast('确认收货成功')
    }, 1000)
  } catch (error) {
    console.log('用户取消确认收货')
  }
}

const onBuyClick = (item) => {
  console.log('再次购买:', item)
  showToast('再次购买功能')
}



// 组件挂载时
onMounted(() => {
  // 从路由参数中获取keyword值
  const keyword = route.query.keyword
  if (keyword) {
    searchKeyword.value = keyword
    showSkeleton.value = true
  }
  // 添加滚动事件监听
  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', handleScroll)
  } else {
    window.addEventListener('scroll', handleScroll)
  }

  // 初始加载数据
  if (keyword) {
    onLoad()
  }
})

// 组件卸载时
onUnmounted(() => {
  // 移除滚动事件监听
  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', handleScroll)
  } else {
    window.removeEventListener('scroll', handleScroll)
  }
  clearAllCountdowns()
})

// 页面激活时触发
onActivated(() => {
  // 页面重新显示时，恢复滚动位置
  console.log('搜索页面激活')
  nextTick(() => {
    if (contentRef.value && scrollPositions.value.all > 0) {
      contentRef.value.scrollTop = scrollPositions.value.all
    }
  })
})

// 页面失活时触发
onDeactivated(() => {
  console.log('搜索页面失活')
  // 保存当前滚动位置
  if (contentRef.value) {
    scrollPositions.value.all = contentRef.value.scrollTop
  }
})
</script>

<style scoped lang="less">
.order-search {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.order-search-content {
  flex: 1;
  overflow: auto;
  background-color: #f5f5f5;
  padding: 10px;
}

.order-item {
  margin-bottom: 10px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 11px;

  .order-shop {
    font-size: @font-size-14;
    font-weight: bold;
    color: @text-color-primary;
  }

  .order-number-container {
    display: flex;
    align-items: center;
    margin-right: 15px;
    width: 100%;
    overflow: hidden;

    .order-number-text {
      font-size: @font-size-11;
      color: @text-color-secondary;
      margin-right: 3px;
      .ellipsis()
    }

    .copy-icon {
      width: 10px;
      height: 10px;
      cursor: pointer;
    }
  }


  .countdown-container {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    color: @theme-color;

    .countdown-text {
      font-size: @font-size-14;
      font-weight: @font-weight-600;
      margin-right: 4px;
      color: @theme-color;
    }

    .countdown-time {
      font-size: @font-size-14;
      font-weight: @font-weight-600;
      color: @theme-color;
    }
  }


  .order-status {
    flex-shrink: 0;
    font-size: @font-size-14;
    font-weight: @font-weight-600;

    &.status-unpaid {
      color: @theme-color;
    }

    &.status-unshipped {
      color: #2196f3;
    }

    &.status-shipped {
      color: #4caf50;
    }

    &.status-completed {
      color: @text-color-secondary;
    }
  }
}

.goods-section {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

// 骨架屏样式
.skeleton-container {
  .skeleton-order {
    .skeleton-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 11px;

      .skeleton-order-number {
        width: 120px;
        height: 12px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4px;
      }

      .skeleton-status {
        width: 60px;
        height: 16px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4px;
      }
    }

    .skeleton-goods {
      display: flex;
      margin-bottom: 15px;

      .skeleton-image {
        width: 75px;
        height: 75px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 8px;
        margin-right: 12px;
        flex-shrink: 0;
      }

      .skeleton-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .skeleton-title {
          width: 80%;
          height: 16px;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: 4px;
          margin-bottom: 8px;
        }

        .skeleton-subtitle {
          width: 60%;
          height: 14px;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: 4px;
          margin-bottom: 8px;
        }

        .skeleton-price {
          width: 40%;
          height: 16px;
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: 4px;
        }
      }
    }

    .skeleton-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 8px;

      .skeleton-button {
        width: 60px;
        height: 28px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 14px;
      }
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 40px 20px;

  .empty-content {
    text-align: center;

    .empty-image {
      width: 120px;
      height: 120px;
      margin-bottom: 16px;
    }

    .empty-text {
      font-size: @font-size-14;
      color: @text-color-secondary;
      line-height: 1.5;
    }
  }
}

// 骨架屏样式
.skeleton-container {
  .skeleton-order {
    padding: 0;
  }

  .skeleton-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;

    .skeleton-order-number {
      width: 120px;
      height: 12px;
      background: #f0f0f0;
      border-radius: 6px;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }

    .skeleton-status {
      width: 60px;
      height: 16px;
      background: #f0f0f0;
      border-radius: 8px;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }
  }

  .skeleton-goods {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;

    .skeleton-image {
      width: 75px;
      height: 75px;
      background: #f0f0f0;
      border-radius: 8px;
      margin-right: 12px;
      flex-shrink: 0;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }

    .skeleton-content {
      flex: 1;
      min-height: 75px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .skeleton-title {
        width: 80%;
        height: 16px;
        background: #f0f0f0;
        border-radius: 8px;
        margin-bottom: 8px;
        animation: skeleton-loading 1.5s ease-in-out infinite;
      }

      .skeleton-subtitle {
        width: 60%;
        height: 12px;
        background: #f0f0f0;
        border-radius: 6px;
        margin-bottom: 8px;
        animation: skeleton-loading 1.5s ease-in-out infinite;
      }

      .skeleton-price {
        width: 40%;
        height: 14px;
        background: #f0f0f0;
        border-radius: 7px;
        animation: skeleton-loading 1.5s ease-in-out infinite;
      }
    }
  }

  .skeleton-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    .skeleton-button {
      width: 60px;
      height: 28px;
      background: #f0f0f0;
      border-radius: 14px;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }
  }
}

@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.6;
  }

  100% {
    opacity: 1;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;

  .empty-content {
    text-align: center;

    .empty-image {
      width: 160px;
      height: 140px;
      margin-bottom: 16px;
    }

    .empty-text {
      font-size: @font-size-14;
      color: @text-color-secondary;
      margin: 0;
    }
  }
}
</style>
